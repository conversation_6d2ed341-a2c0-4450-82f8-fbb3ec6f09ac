import pandas as pd
import yfinance as yf
import matplotlib.pyplot as plt
import numpy as np

PREMIUM = 240
RANGE_MULTIPLIER = 1.5

# Scarica dati settimanali SPY
spy = yf.download('ES=F', start='2010-01-01', end='2026-08-04', interval='1wk', auto_adjust=True)

# Rimuove MultiIndex se presente
if isinstance(spy.columns, pd.MultiIndex):
    spy.columns = spy.columns.droplevel(1)

# drop Volume column
spy.drop(columns=['Volume'], inplace=True)
spy = spy.resample('W-MON').first()

# Calcoli base
spy['range'] = spy['High'] - spy['Low']
spy['body'] = abs(spy['Close'] - spy['Close'].shift(1))

spy['avg_range_prev3'] = spy['range'].shift(1).rolling(window=3).mean()*RANGE_MULTIPLIER

spy.dropna(inplace=True)

filter = spy['range'].shift(1) < spy['range'].shift(2)
spy = spy[filter]

spy['trade'] = np.where(spy['body'] > spy['avg_range_prev3'], abs(spy['body']-spy['avg_range_prev3'])*50*-1 , PREMIUM)

# print lost trade
loss_trade = spy[spy['trade'] < 0]
print(spy[['Close', 'avg_range_prev3', 'body', 'trade']])


# plot equity
spy['equity'] = spy['trade'].cumsum()
plt.figure(figsize=(14, 6))
plt.plot(spy.index, spy['equity'], label='Equity', color='blue')
plt.title("Equity Curve")
plt.show()
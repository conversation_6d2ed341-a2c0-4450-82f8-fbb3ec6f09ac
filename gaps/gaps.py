# user yfinance to download data from Yahoo Finance
import yfinance as yf
import pandas as pd
import matplotlib.pyplot as plt

CAPITAL = 1000

# Download data from Yahoo Finance
def download_data(ticker, start, end):
    data = yf.download(ticker, start=start, end=end)
    data.reset_index(inplace=True)
    data.columns = data.columns.droplevel(1)
    return data[['Date', 'Open', 'Close', 'High', 'Low']]

# Calculate gaps
def calculate_gaps(data):
    data['condition'] = True
    # condition only if date is monday
    # data['condition'] = (data['Close']>data['Close'].rolling(window=100).mean()).astype(bool)
    # data['condition'] = (data['Close']<data['Low'].shift(1)).astype(bool)
    # condition VIX > 50
    data['condition'] = ( (data['VIX']<10) ).astype(bool)
    data['Gap'] = data['Open'] - data['Close'].shift(1)
    data['Gap %'] = (data['Gap'] / data['Close'].shift(1)) * 100
    data['Contract'] = (CAPITAL/data['Close'].shift(1).fillna(1)).astype(int)
    data['pl'] = data['Gap']*data['Contract']
    # take only the rows where the previous day was positive
    data = data[data['condition']]
    print(data.shape)
    data['equity'] = data['pl'].cumsum()
    return data

def larry_williams_vix_fix_formula(data):
    # Larry Williams VIX Fix formula
    data['VIX'] = (data['High'].rolling(window=22).max() - data['Close']) / (data['High'].rolling(window=22).max() - data['Low'].rolling(window=22).min()) * 100
    data['VIX'] = data['VIX'].fillna(0)
    data['VIX'] = data['VIX'].clip(lower=0)
    return data

# stimate historical vix based on har_rv formula
def har_rv(data):
    data['VIX'] = (data['High'] - data['Low']) / (data['High'].rolling(window=22).max() - data['Low'].rolling(window=22).min()) * 100
    data['VIX'] = data['VIX'].fillna(0)
    data['VIX'] = data['VIX'].clip(lower=0)
    return data

# Plot gaps
def plot_gaps(data):
    plt.figure(figsize=(10, 5))
    # plt.plot(data['Date'], data['Gap'], label='Gap', color='blue')
    plt.plot(data['Date'], data['Gap %'], label='Gap %', color='red')
    plt.title('Gaps in Stock Prices')
    plt.xlabel('Date')
    plt.ylabel('Gap / Gap %')
    plt.legend()
    plt.grid()
    plt.savefig('gaps.png')

# function that plot distribution of gaps
def plot_distribution(data):
    plt.figure(figsize=(10, 5))
    plt.hist(data['Gap %'], bins=100, alpha=0.7, color='blue')
    plt.title('Distribution of Gaps %')
    plt.xlabel('Gap %')
    plt.ylabel('Frequency')
    plt.grid()
    plt.savefig('gaps_distribution.png')

# function that plot equity
def plot_equity(data):
    plt.figure(figsize=(10, 5))
    plt.plot(data['Date'], data['equity'], label='Equity', color='green')
    plt.title('Equity Curve')
    plt.xlabel('Date')
    plt.ylabel('Equity')
    plt.legend()
    plt.grid()
    plt.savefig('equity_curve.png')
    # Plot equity and VIX in two separate charts in a column
    fig, (ax1, ax2) = plt.subplots(nrows=2, ncols=1, figsize=(10, 10), sharex=True)

    ax1.plot(data['Date'], data['equity'], label='Equity', color='green')
    ax1.set_ylabel('Equity', color='green')
    ax1.set_title('Equity Curve')
    ax1.legend()
    ax1.grid()

    ax2.plot(data['Date'], data['VIX'], label='VIX', color='purple', alpha=0.6)
    ax2.set_xlabel('Date')
    ax2.set_ylabel('VIX', color='purple')
    ax2.set_title('Larry Williams VIX Fix')
    ax2.legend()
    ax2.grid()

    fig.tight_layout()
    plt.savefig('equity_vix_curve.png')

# Main function
def main():
    ticker = 'JNJ'
    start = '2010-01-01'
    end = '2023-12-31'
    data = download_data(ticker, start, end)
    data = har_rv(data)
    data = calculate_gaps(data)
    #print(data.tail(40))
    plot_gaps(data)
    plot_distribution(data)
    plot_equity(data)

    print(data['pl'].mean()/data['Contract'].std())

if __name__ == '__main__':
    main()
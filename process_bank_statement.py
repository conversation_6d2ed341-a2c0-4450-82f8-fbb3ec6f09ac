#!/usr/bin/env python3
"""
Script to process bank statement CSV file:
- Remove August entries
- Categorize expenses by category and month
"""

import csv
import pandas as pd
from datetime import datetime
from collections import defaultdict
import locale

def parse_amount(amount_str):
    """Parse amount string to float, handling Italian number format"""
    # Remove EUR and whitespace
    amount_str = amount_str.replace('EUR', '').strip()
    # Replace comma with dot for decimal separator
    amount_str = amount_str.replace('.', '').replace(',', '.')
    return float(amount_str)

def parse_date(date_str):
    """Parse date string in DD/MM/YYYY format"""
    return datetime.strptime(date_str, '%d/%m/%Y')

def process_bank_statement(input_file, output_file):
    """Process the bank statement CSV file"""
    
    # Read the CSV file
    data = []
    with open(input_file, 'r', encoding='utf-8') as file:
        # The file doesn't seem to have headers, so we'll define them
        reader = csv.reader(file, delimiter='\t')
        
        for row in reader:
            if len(row) >= 8:  # Ensure we have all columns
                data.append(row)
    
    # Filter out August entries and process data
    filtered_data = []
    monthly_categories = defaultdict(lambda: defaultdict(float))
    
    for row in data:
        try:
            date_str = row[0]
            transaction_type = row[1]
            description = row[2]
            account = row[3]
            status = row[4]
            category = row[5]
            currency = row[6]
            amount_str = row[7]
            
            # Parse date
            date_obj = parse_date(date_str)
            
            # Skip August entries (month 8)
            if date_obj.month == 8:
                continue
                
            # Parse amount
            amount = parse_amount(amount_str)
            
            # Add to filtered data
            filtered_data.append(row)
            
            # Categorize by month and category
            month_year = date_obj.strftime('%Y-%m')
            monthly_categories[month_year][category] += amount
            
        except (ValueError, IndexError) as e:
            print(f"Error processing row: {row[:3]}... - {e}")
            continue
    
    # Create summary report
    with open(output_file, 'w', encoding='utf-8') as file:
        file.write("ESTRATTO CONTO ELABORATO - AGOSTO RIMOSSO\n")
        file.write("=" * 50 + "\n\n")
        
        # Summary by month and category
        file.write("RIEPILOGO PER MESE E CATEGORIA\n")
        file.write("-" * 40 + "\n\n")
        
        # Sort months chronologically
        sorted_months = sorted(monthly_categories.keys())
        
        total_by_month = {}
        total_by_category = defaultdict(float)
        
        for month in sorted_months:
            file.write(f"MESE: {month}\n")
            file.write("-" * 20 + "\n")
            
            month_total = 0
            categories = monthly_categories[month]
            
            # Sort categories by amount (expenses first, then income)
            sorted_categories = sorted(categories.items(), key=lambda x: x[1])
            
            for category, amount in sorted_categories:
                file.write(f"{category:40} {amount:>12.2f} EUR\n")
                month_total += amount
                total_by_category[category] += amount
            
            file.write("-" * 55 + "\n")
            file.write(f"{'TOTALE MESE':40} {month_total:>12.2f} EUR\n")
            file.write("\n")
            
            total_by_month[month] = month_total
        
        # Overall summary by category
        file.write("\nRIEPILOGO GENERALE PER CATEGORIA\n")
        file.write("=" * 40 + "\n")
        
        sorted_total_categories = sorted(total_by_category.items(), key=lambda x: x[1])
        grand_total = 0
        
        for category, amount in sorted_total_categories:
            file.write(f"{category:40} {amount:>12.2f} EUR\n")
            grand_total += amount
        
        file.write("-" * 55 + "\n")
        file.write(f"{'TOTALE GENERALE':40} {grand_total:>12.2f} EUR\n")
        
        # Monthly totals summary
        file.write("\n\nRIEPILOGO MENSILE\n")
        file.write("=" * 25 + "\n")
        
        for month in sorted_months:
            file.write(f"{month:15} {total_by_month[month]:>12.2f} EUR\n")
    
    # Save filtered CSV data (without August)
    filtered_csv_file = input_file.replace('.csv', '_no_august.csv')
    with open(filtered_csv_file, 'w', encoding='utf-8', newline='') as file:
        writer = csv.writer(file, delimiter='\t')
        
        # Write header
        writer.writerow(['Data', 'Tipo', 'Descrizione', 'Conto', 'Stato', 'Categoria', 'Valuta', 'Importo'])
        
        # Write filtered data
        for row in filtered_data:
            writer.writerow(row)
    
    print(f"Elaborazione completata!")
    print(f"Report salvato in: {output_file}")
    print(f"CSV filtrato salvato in: {filtered_csv_file}")
    print(f"Righe originali: {len(data)}")
    print(f"Righe dopo rimozione agosto: {len(filtered_data)}")

if __name__ == "__main__":
    input_file = "estratto.csv"
    output_file = "riepilogo_spese.txt"
    
    process_bank_statement(input_file, output_file)

import yfinance as yf
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import date, timedelta
import requests

def download_data_from_yfinance(ticker, start_date, end_date):
    data = yf.download(ticker, start=start_date, end=end_date, auto_adjust=True)
    data.columns = data.columns.droplevel(1)
    return data

def plot_yearly_returns(yearly_returns: pd.Series):
    plt.figure(figsize=(12,6))
    yearly_returns.plot(kind='bar', color='skyblue', edgecolor='black')
    plt.axhline(0, color='red', linestyle='--')
    plt.title('Rendimenti stagionali anno per anno')
    plt.xlabel('Anno')
    plt.ylabel('Rendimento')
    plt.grid(axis='y', linestyle='--', alpha=0.7)
    # plt.show()

def plot_heatmap(data, xlabels, ylabels, title, xlabel, ylabel, cmap='RdYlGn'):
    plt.figure(figsize=(12, 6))
    plt.imshow(data, aspect='auto', cmap=cmap, origin='lower')
    plt.colorbar(label=title)
    plt.xticks(ticks=np.arange(len(xlabels)), labels=[f"{x:+d}" for x in xlabels], rotation=45)
    plt.yticks(ticks=np.arange(len(ylabels)), labels=[f"{y:+d}" for y in ylabels])
    plt.xlabel(xlabel)
    plt.ylabel(ylabel)
    plt.title(title)
    plt.tight_layout()
    # plt.show()

def send_telegram_message(bot_token: str, chat_id: str, message: str):
    url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
    payload = {
        "chat_id": chat_id,
        "text": message,
        "parse_mode": "HTML"
    }
    try:
        response = requests.post(url, data=payload)
        if response.status_code != 200:
            print(f"Errore invio Telegram: {response.status_code} - {response.text}")
        else:
            print("Messaggio Telegram inviato con successo!")
    except Exception as e:
        print(f"Errore invio Telegram: {e}")

def get_sp500_contituents_from_wikipedia():
    url = "https://en.wikipedia.org/wiki/List_of_S%26P_500_companies"
    tables = pd.read_html(url)
    df = tables[0]  # Prima tabella
    return df['Symbol'].tolist()


from Seasonal import Seasonal
import sys

if __name__ == "__main__":
    CHAT_ID = '-1002888926663'
    BOT_TK = '8377896769:AAEGKm-R7latXY7EyA-9IKPbx3ggzRmljPI'

    tickers = [sys.argv[1]] if len(sys.argv) > 1 else get_sp500_contituents_from_wikipedia()
    MONTH = sys.argv[2] if len(sys.argv) > 2 else date.today().month
    DAY = sys.argv[3] if len(sys.argv) > 3 else date.today().day
    PERIODS = [sys.argv[4]] if len(sys.argv) > 4 else [30, 40, 60, 90]

    detail = False
    if len(tickers) == 1:
        detail = True
        print(tickers, MONTH, DAY, PERIODS)

    messages  = []
    analyzed_tikers = []
    for ticker in tickers:
        close = download_data_from_yfinance(ticker, '1970-01-01', '2026-08-04')['Close']
        if len(close) == 0 or len(close.index.year.unique())<20:
            continue
        print(ticker)
        for period in PERIODS:
            if ticker in analyzed_tikers:
                continue

            seasonal = Seasonal(close, MONTH, DAY, period)
            if seasonal.is_valid(0.02, 0.05):
                stats = seasonal.get_stats()
                messages.append(f"Stagionalità su <b>{ticker}</b> da {DAY}/{MONTH} per {period} giorni. Avg ret. {stats['mean_return']:.2%} % succ: {stats['success_rate']:.2%} p-value: {stats['p_value']:.4f}")

                analyzed_tikers.append(ticker)
                if detail == True:
                    plot_yearly_returns(seasonal.get_yearly_returns())

                    heatmap_return, heatmap_success, days_offsets, periods_offsets = seasonal.optimize()
                    plot_heatmap(heatmap_return, days_offsets, periods_offsets,
                                title="Rendimento medio (%)",
                                xlabel="Offset giorni inizio",
                                ylabel="Offset periodo (giorni)",
                                cmap='RdYlGn')

                    plot_heatmap(heatmap_success, days_offsets, periods_offsets,
                                title="% Successo (rendimenti > 0)",
                                xlabel="Offset giorni inizio",
                                ylabel="Offset periodo (giorni)",
                                cmap='RdYlGn')
                    plt.show()


            """
                    # send_telegram_message(BOT_TK, CHAT_ID, message)
            """
    print('\n'.join(messages))
    # send_telegram_message(BOT_TK, CHAT_ID, '\n'.join(messages))




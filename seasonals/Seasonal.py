import pandas as pd
import numpy as np
from datetime import timedelta

class Seasonal:

    n_bootstrap: int = 5000

    def __init__(self, close: pd.Series, month:int, day:int, period:int):
        self.close = close.copy().resample('D').ffill()
        self.month = int(month)
        self.day = int(day)
        self.period = int(period)


        self.intervals = self.get_intervals()
        self.returns = [self.close[dd[1]]/self.close[dd[0]]-1 for dd in self.intervals]
        self.direcion = "long" if np.array(self.returns).mean() > 0 else "short"
        self.p_value = None

    def get_intervals(self):
        return [(d, d+timedelta(days=self.period)) for d in self.close.index if d.month == self.month and d.day == self.day and (d+timedelta(days=self.period)) < self.close.index[-1]]

    def bootstrap_test(self):
        random_means = []
        for _ in range(self.n_bootstrap):
            iteration_mean = []
            for _ in self.intervals:
                random_index = np.random.randint(0, len(self.close.index)-self.period)
                sample = self.close.iloc[random_index:random_index+self.period]
                iteration_mean.append(sample.iloc[-1]/sample.iloc[0]-1)
            random_means.append(np.mean(iteration_mean))
        return np.array(random_means)

    def is_valid(self, avg_return_limit:float, p_value_level:float = 0.05):
        avg_return = np.array(self.returns).mean()

        if abs(avg_return) < avg_return_limit:
            return False

        bootstrap_returns = self.bootstrap_test()
        if self.direcion == "long":
            self.p_value = (bootstrap_returns >= avg_return).mean()
        else:
            self.p_value = (bootstrap_returns <= avg_return).mean()

        return self.p_value <= p_value_level

    def get_yearly_returns(self):
        returns = np.array(self.returns)
        years = [d.year for d, _ in self.intervals]
        return pd.Series(returns, index=years).sort_index()

    def optimize(self, day_range=5, period_range=5):

        """
        Calcola matrici di rendimento medio e % successi testando variazioni di giorno di inizio e durata periodo.
        day_range e period_range definiscono lo scostamento massimo +/- da base_day e base_period.
        """
        days_offsets = range(-day_range, day_range+1)
        periods_offsets = range(-period_range, period_range+1)

        heatmap_return = np.zeros((len(periods_offsets), len(days_offsets)))
        heatmap_success = np.zeros((len(periods_offsets), len(days_offsets)))

        for i, period_offset in enumerate(periods_offsets):
            period = self.period + period_offset
            if period <= 0:
                heatmap_return[i, :] = np.nan
                heatmap_success[i, :] = np.nan
                continue
            for j, day_offset in enumerate(days_offsets):
                # Calcolo mese e giorno spostato
                # Nota: potrebbe sforare fine mese, per semplicità salvo in try-except
                try:
                    # Trova tutte le date che corrispondono a (month, day+offset)
                    intervals = [(d, d + timedelta(days=period)) for d in self.close.index
                                if d.month == self.month and (d.day + day_offset) == d.day and (d + timedelta(days=period)) < self.close.index[-1]]
                    # Nota: la condizione (d.day + day_offset) == d.day non funziona per offset
                    # Serve correggere: cerchiamo date in cui giorno == base_day + day_offset, gestendo over/underflow
                    # Correggo sotto con filtro più corretto

                    # Ricostruzione filtro date per mese e giorno modificato
                    day = self.day + day_offset
                    valid_dates = []
                    for d in self.close.index:
                        if d.month == self.month:
                            # controllo giorno dentro il mese (anche mese bisestile)
                            if day > 0 and day <= (pd.Timestamp(year=d.year, month=self.month, day=1) + pd.offsets.MonthEnd(0)).day:
                                if d.day == day:
                                    valid_dates.append(d)
                    intervals = [(d, d + timedelta(days=period)) for d in valid_dates if (d + timedelta(days=period)) < self.close.index[-1]]

                    returns = [self.close[end]/self.close[start] - 1 for start, end in intervals]
                    if len(returns) == 0:
                        heatmap_return[i, j] = np.nan
                        heatmap_success[i, j] = np.nan
                    else:
                        mean_return = np.mean(returns)
                        heatmap_return[i, j] = mean_return
                        # Successo = % di rendimenti > 0
                        heatmap_success[i, j] = np.mean(np.array(returns) > 0)
                except Exception:
                    heatmap_return[i, j] = np.nan
                    heatmap_success[i, j] = np.nan

        return heatmap_return, heatmap_success, days_offsets, periods_offsets


    def get_stats(self):
        returns = np.array(self.returns)

        return {
            "returns": returns,
            "count": len(returns),
            "mean_return": returns.mean(),
            "std_return": returns.std(),
            "min_return": returns.min() if self.direcion == "long" else returns.max(),
            "max_return": returns.max() if self.direcion == "long" else returns.min(),
            "success_rate": (returns > 0).mean() if self.direcion == "long" else (returns < 0).mean(),
            "p_value": self.p_value,
        }


import pandas as pd
import matplotlib.pyplot as plt


CAPITAL = 100000

BASE_PATH = './ts_importer'

df = pd.read_csv(f'{BASE_PATH}/short_es_2.csv', skiprows=4, header=0, usecols=[0, 1, 2, 7], date_format='%d/%m/%Y', parse_dates=[2])
df.rename(columns={'#' :'Nr' ,'Date/Time': 'Start', 'Net Profit - Cum Net Profit': 'PL',}, inplace=True)

df['End'] = df['Start'].shift(-1)
# drop lines with NaN in 'Nr' column
df.dropna(subset=['Nr'], inplace=True)

df = df[['Nr', 'Type', 'Start', 'End', 'PL']]
# format PL in negative with this example ($500.00) = -500.00
# else if positive with this example ($500.00) = 500.00
def format_pl(pl):
    if pl.startswith('(') and pl.endswith(')'):
        return -float(pl[1:-1].replace('$', '').replace(',', ''))
    else:
        return float(pl.replace('$', '').replace(',', ''))

# filter by End date from 2021-01-01 to 2025-12-31
#df = df[(df['End'] >= '2025-01-01') & (df['End'] <= '2025-12-31')]

df['PL_f'] = df['PL'].apply(format_pl)
print(df.head())
df['equity'] = df['PL_f'].cumsum()

equity = df[['End', 'equity']].copy().set_index('End')['equity']
drawdown = equity - equity.cummax()
print(drawdown)
# plot equity and drawdown
plt.figure(figsize=(10, 5))
plt.subplot(2, 1, 1)
plt.plot(equity)
plt.fill_between(equity.index, equity, 0, alpha=0.2)
plt.title('Equity')
plt.subplot(2, 1, 2)
plt.plot(drawdown, color='red')
plt.fill_between(drawdown.index, drawdown, 0, color='red', alpha=0.2)
plt.title('Drawdown')
plt.tight_layout()
plt.savefig(f'{BASE_PATH}/equity.png')
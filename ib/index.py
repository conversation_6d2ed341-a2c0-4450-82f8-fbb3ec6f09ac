from ib_insync import *
import pandas as pd

ib = IB()
ib.connect('127.0.0.1', 7496, clientId=1)

# 1. Risolvi il contratto base AAPL
aapl = Stock('AAPL', 'SMART', 'USD')
details = ib.reqContractDetails(aapl)
conId = details[0].contract.conId

# Get current stock price
stock_ticker = ib.reqMktData(aapl)
ib.sleep(2)
current_price = stock_ticker.last or stock_ticker.close or (stock_ticker.bid + stock_ticker.ask) / 2
print(f"Current AAPL price: ${current_price:.2f}")

# 2. Scarica catena opzioni
chains = ib.reqSecDefOptParams(aapl.symbol, '', aapl.secType, conId)

# 3. Get market data for options (limited to avoid API overload)
data = []
processed = 0
max_options = 50  # Limit to avoid too many requests

for chain in chains:
    if processed >= max_options:
        break

    # Take first 2 expirations
    for exp in chain.expirations[:2]:
        if processed >= max_options:
            break

        # Filter strikes near current price (within 15%)
        nearby_strikes = [s for s in chain.strikes
                         if abs(s - current_price) / current_price <= 0.15]
        nearby_strikes = sorted(nearby_strikes, key=lambda x: abs(x - current_price))[:5]

        for strike in nearby_strikes:
            if processed >= max_options:
                break

            # Get both calls and puts
            for right in ['C', 'P']:
                if processed >= max_options:
                    break

                try:
                    # Create option contract
                    option = Option(aapl.symbol, exp, strike, right, 'SMART',
                                  tradingClass=chain.tradingClass)

                    # Qualify the contract
                    qualified = ib.qualifyContracts(option)
                    if not qualified:
                        continue

                    option = qualified[0]

                    # Request market data
                    ticker = ib.reqMktData(option)
                    ib.sleep(0.3)  # Wait for data

                    # Add to data if we have bid/ask
                    if ticker.bid and ticker.ask and ticker.bid > 0:
                        data.append({
                            'tradingClass': chain.tradingClass,
                            'multiplier': chain.multiplier,
                            'expiration': exp,
                            'strike': strike,
                            'right': right,
                            'bid': ticker.bid,
                            'ask': ticker.ask,
                            'mid': (ticker.bid + ticker.ask) / 2,
                            'last': ticker.last
                        })
                        processed += 1
                        print(f"Added {right} {strike} {exp}: Bid={ticker.bid}, Ask={ticker.ask}")

                    # Cancel market data to free resources
                    ib.cancelMktData(option)

                except Exception as e:
                    print(f"Error with {right} {strike} {exp}: {e}")
                    continue

# Cancel stock market data
ib.cancelMktData(aapl)

# Create DataFrame
df = pd.DataFrame(data)
if not df.empty:
    df = df.sort_values(['expiration', 'strike', 'right'])
    print(f"\nFound {len(df)} options with market data:")
    print(df.to_string(index=False))

    # Save to CSV
    df.to_csv('ib/aapl_options_with_prices.csv', index=False)
    print(f"\nData saved to ib/aapl_options_with_prices.csv")
else:
    print("No option data retrieved")

ib.disconnect()

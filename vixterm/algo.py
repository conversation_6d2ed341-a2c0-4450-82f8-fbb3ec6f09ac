
# get all file sin to ./download folder
import os

import pandas as pd
import re
import matplotlib.pyplot as plt
from itertools import combinations

BASE_PATH = os.path.dirname(os.path.abspath(__file__))
DOWNLOAD_FOLDER = os.path.join(BASE_PATH, 'download')


# LOAD DATA
df = pd.read_csv(DOWNLOAD_FOLDER + '/vixterm.csv', index_col=0)
df.index = pd.to_datetime(df.index)
expirations_df = pd.read_csv(DOWNLOAD_FOLDER + '/expirations.csv', index_col=0)
expirations_df['expiration_date'] = pd.to_datetime(expirations_df['expiration_date'])
expirations_by_col = expirations_df['expiration_date'].to_dict()


# GENERATE ALL COUPLE EXPIRATIONS POSSIBILITIES
SELECTIONS = []
base_exp = [1,2,3,4,5]
for r in range(1, len(base_exp)+1):
    SELECTIONS.extend(combinations(base_exp, r))
SELECTIONS = [p for p in SELECTIONS if len(p) == 2 and p[1] <= p[0]+2]

#SELECTIONS = [(5,7), (6,7)]  # only 2 combinations for now
# SELECTIONS = [(6,7)]  # only 2 combinations for now

# PARAMS
FROM = None
TO = None
EXIT_PERIOD = 30 # 30 days
MIN_DAYS_TO_EXPIRATION = 31 # at least days to expirations
MAX_DAYS_TO_EXPIRATION = 2000 # max days to expirations
SL = 5000
TP = 2000
B_LEVEL = 0

WEIGHTS = [1,1,3,2,4,3,8]  # weights for each expiration in the selection

# ensure MIN_DAYS_TO_EXPIRATION is not less than EXIT_PERIOD
MIN_DAYS_TO_EXPIRATION = max(MIN_DAYS_TO_EXPIRATION, EXIT_PERIOD+1)  # ensure it's not negative

if FROM:
    df = df[(df.index > FROM)]
if TO:
    df = df[(df.index < TO)]


plt.figure(figsize=(12, 6))
report = dict()
equities = []
for case, SELECTION in enumerate(SELECTIONS):
    weight = WEIGHTS[case]

    # find expirations within MIN_DAYS_TO_EXPIRATION and MAX_DAYS_TO_EXPIRATION days to expirations
    # and select only those expirations that are in the SELECTION
    available_expirations = dict()
    for date, row in df.iterrows():
        expirations = [exp for i, exp in enumerate(row.dropna().index) if (expirations_by_col[exp]-date).days >= MIN_DAYS_TO_EXPIRATION and (expirations_by_col[exp]-date).days <= MAX_DAYS_TO_EXPIRATION]
        if SELECTION:
            expirations = [exp for i, exp in enumerate(expirations) if (i+1) in SELECTION]
        available_expirations[date] = expirations

    # find relative value for each expiration available per every day
    expirations_values= dict()
    for date, expirations in available_expirations.items():
        expirations_values[date] = [df.loc[date, exp] for exp in expirations]

    # BUILD TRADES
    trades = []
    in_trade_expirations = []
    for date, row in df.iterrows():
        values = expirations_values.get(date, [])
        exps = available_expirations.get(date, [])

        # Verso la fine può succedere che su scadenze lontane non ci sia ancora l'ultima scadena
        if len(values) != 2 or len(exps) != 2:
            print(f'Skipping date {date} with values {values} and expirations {exps}')

        if len(values) == 2 and len(exps) == 2 and values[1] - values[0] < B_LEVEL and exps not in in_trade_expirations:
            in_trade_expirations.append(exps)
            trades.append({
                'date': date,
                'sell_exp_1': exps[0],
                'buy_exp_2': exps[1],
                'entry_exp_1': values[0],
                'entry_exp_2': values[1],
                'exit_date': None,
                'exit_exp_1': None,
                'exit_exp_2': None,
                'profit': None,
                'selection': SELECTION,
                'real': False
            })
        for trade in trades:
            if trade['exit_date'] :
                # print(f"Trade on {trade['date']} already exited on {trade['exit_date']}, skipping")
                continue

            trade['exit_exp_1'] = df.loc[date, trade['sell_exp_1']]
            trade['exit_exp_2'] = df.loc[date, trade['buy_exp_2']]

            trade['profit'] = (trade['entry_exp_1'] - trade['exit_exp_1']) + (trade['exit_exp_2'] - trade['entry_exp_2'])
            trade['profit'] = round(trade['profit']*weight*1000, 2)

            if  trade['profit'] > 0 and not trade['real']:
                trade['real'] = True
                trade['entry_exp_1'] = df.loc[date, trade['sell_exp_1']]
                trade['entry_exp_2'] = df.loc[date, trade['buy_exp_2']]
                trade['profit'] = 0

            sl_condition = (SL and trade['profit']  <= -SL*weight)
            tp_condition = (TP and trade['profit']  >= TP*weight)

            if trade['real'] and ((date - trade['date']).days >= EXIT_PERIOD or sl_condition or tp_condition):
                if not sl_condition and not tp_condition:
                    in_trade_expirations.remove([trade['sell_exp_1'], trade['buy_exp_2']])
                trade['exit_date'] = date



    # clean trades, remove those that are not real
    fake_trades = [trade for trade in trades if trade['real']==False]
    trades = [trade for trade in trades if trade['real']]
    print(len(fake_trades))

    # create a DataFrame from trades
    trades = pd.DataFrame(trades)
    try:
        profits = trades[['exit_date', 'profit']].copy()
    except:
        print(trades)
        break


    # filter from 2016
    # profits = profits[profits['exit_date'] >= '2016-01-01']


    profits['exit_date'] = pd.to_datetime(profits['exit_date'])
    profit_daily = profits.groupby('exit_date').sum().round(2)
    equity = profit_daily['profit'].cumsum()
    equities.append(equity)

    report[SELECTION] = {
        'total_profit': profits['profit'].sum().round(2),
        'total_trades': len(profits),
        'avg_trade': profits['profit'].mean().round(2),
        'win_trades': len(profits[profits['profit'] > 0]),
        'loss_trades': len(profits[profits['profit'] < 0]),
        'win_rate': round(len(profits[profits['profit'] > 0]) / len(profits) * 100,2) if len(profits) > 0 else 0,
        'max_loss': profits['profit'].min(),
        'max_profit': profits['profit'].max(),
        'max_drawdown_abs': (equity - equity.cummax()).min(),
        'sharpe_ratio': (profits['profit'].mean() / profits['profit'].std()).round(2) if profits['profit'].std() != 0 else 0,
        'consecutive_wins': (profits['profit'] > 0).astype(int).groupby((profits['profit'] <= 0).cumsum()).transform('count').max(),
        'consecutive_losses': (profits['profit'] < 0).astype(int).groupby((profits['profit'] >= 0).cumsum()).transform('count').max(),
        'params': f"{'_'.join(map(str, SELECTION))}_from_{FROM.strftime('%Y%m%d') if FROM else 'all'}_to_{TO.strftime('%Y%m%d') if TO else 'all'}_exit_{EXIT_PERIOD}_sl_{SL}_tp_{TP}"
    }

    # Plotting the equity curve

    #plt.plot(equity.index, equity.values, label=f'Equity Curve {SELECTION}')
    print(f"Selection: {SELECTION}")

    trades.to_csv(f"{BASE_PATH}/trades.csv", sep=',', index=False, decimal=',', mode='a')



report_df = pd.DataFrame.from_dict(report, orient='index')
report_df.to_csv(f"{BASE_PATH}/report.csv", sep=',', index=True, decimal=',', mode='a' )

for equity in equities:
    plt.plot(equity.index, equity.values, label=f'Equity Curve {equity.name}')

plt.title('Equity Curve')
plt.xlabel('Date')
plt.ylabel('Equity')
plt.grid()
plt.legend()
plt.show()


total_equity = pd.concat(equities, axis=1).ffill().sum(axis=1)
#plt.plot(total_equity.index, total_equity.values, label='Total Equity Curve', color='black', linewidth=2)


# plot in subset chart equity total and drawdown
plt.figure(figsize=(12, 6))
plt.subplot(2, 1, 1)
plt.plot(total_equity.index, total_equity.values, label='Total Equity Curve', color='black', linewidth=2)
plt.title('Total Equity Curve')
plt.xlabel('Date')
plt.ylabel('Equity')
plt.grid()
plt.legend()
plt.subplot(2, 1, 2)
drawdown = total_equity - total_equity.cummax()
plt.fill_between(drawdown.index, drawdown.values, color='red', alpha=0.5)
plt.title('Drawdown')
plt.xlabel('Date')
plt.ylabel('Drawdown')
plt.grid()
plt.tight_layout()
plt.show()

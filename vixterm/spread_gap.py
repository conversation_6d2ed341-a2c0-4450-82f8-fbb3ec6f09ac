
# Sembra che lo spread verso la fine della prima scadenza (1M) si allarghi ripetto alla sedonda (2M)..
# Quindi vendiamo la prima e compriamo la seconda
import os
import pandas as pd
import matplotlib.pyplot as plt

from dataclasses import dataclass

BASE_PATH = os.path.dirname(os.path.abspath(__file__))
DOWNLOAD_FOLDER = os.path.join(BASE_PATH, 'download')


def get_vixterm_data():
    df = pd.read_csv(DOWNLOAD_FOLDER + '/vixterm.csv', index_col=0)
    df.index = pd.to_datetime(df.index)
    return df

def get_expirations(date: str, residual_life: int = 0):
    residual_life = residual_life or 0

    df = get_vixterm_data()

    # add residual life to date
    date_residual_life = (pd.to_datetime(date) + pd.Timedelta(days=residual_life)).strftime('%Y-%m-%d')


    # intersection between df[date_residual_life:].iloc[0].dropna() and df[date:].iloc[0].dropna()
    expirations = df[date_residual_life:].iloc[0].dropna().index.intersection(df[date:].iloc[0].dropna().index)
    return df.loc[date, expirations].to_dict()


@dataclass
class Trade:
    open_date: str
    open_price: float
    close_date: str = None
    close_price: float = None
    type: str = 'long'  # 'long' or 'short'

    def closed(self):
        return self.close_date is not None



@dataclass
class CalendarTrade:
    buy: tuple
    sell: tuple
    day_since_entry: int = 0
    closed: bool = False

    def update(self, date: str, expirations: dict):
        if not self.buy[0].closed():
            self.buy[0].close_price = expirations[self.buy[1]]

        if not self.sell[0].closed() is None:
            self.sell[0].close_price = expirations[self.sell[1]]

        self.day_since_entry += 1

    def close(self, date: str, expirations: dict):
        if self.closed:
            return

        print(f"Closing spread on {date}: Buy {self.buy[1]} at {self.buy[0].close_price}, Sell {self.sell[1]} at {self.sell[0].close_price}")
        if not self.buy[0].closed():
            self.buy[0].close_date = date
            self.buy[0].close_price = expirations[self.buy[1]]

        if not self.sell[0].closed():
            self.sell[0].close_date = date
            self.sell[0].close_price = expirations[self.sell[1]]

        self.closed = True



class Backtest:
    SPREAD_TO_ENTRY = 2

    def __init__(self):
        self.trades = []

    def open_spread(self, date, exp1, exp2):

        if any(trade.closed is False for trade in self.trades):
            #print(f"Cannot open new spread on {date}, there are open trades.")
            return

        expirations = get_expirations(date, 60)

        value1 = expirations[exp1]
        value2 = expirations[exp2]

        self.trades.append(CalendarTrade(
            buy=(Trade(open_date=date, open_price=value1, type='long'), exp1),
            sell=(Trade(open_date=date, open_price=value2, type='short'), exp2)
        ))
        print(f"Opened spread on {date}: Buy {exp1} at {value1}, Sell {exp2} at {value2}")

    def run(self):
        for date in get_vixterm_data().index.tolist()[-300:-250]:
            # print(f"Processing date: {date}")
            expirations = get_expirations(date, 60)
            today_exps = get_expirations(date)

            for exp1, exp2 in zip(list(expirations.keys())[:-1], list(expirations.keys())[1:]):
                if expirations[exp2] - expirations[exp1] > self.SPREAD_TO_ENTRY:
                    self.open_spread(date, exp1, exp2)

            for trade in self.trades:
                trade.update(date, today_exps)

                if trade.day_since_entry >= 10:
                    trade.close(date, today_exps)

bck = Backtest()
bck.run()

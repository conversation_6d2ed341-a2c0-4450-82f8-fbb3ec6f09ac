

import os
import pandas as pd
import re

def get_all_files_in(dir_path):
    """
    Get all files in a directory and its subdirectories.

    Args:
        dir_path (str): The path to the directory.

    Returns:
        list: A list of file paths.
    """
    file_paths = []
    for root, dirs, files in os.walk(dir_path):
        for file in files:
            file_paths.append(os.path.join(root, file))

    # sort by name
    file_paths.sort()

    return file_paths

def futures_to_colname(futures_str):
    # Example: "G (Feb 2013)" -> "feb_13"
    match = re.search(r'\((\w+)\s+(\d{4})\)', futures_str)
    if match:
        month = match.group(1).lower()
        year = match.group(2)[-2:]  # last two digits
        return f"{month}_{year}"
    return futures_str

def load_and_pivot_vixterm_csv(filepath):
    df = pd.read_csv(filepath)
    df = df[['Trade Date', 'Close', 'Futures']]
    df['Futures'] = df['Futures'].apply(futures_to_colname)
    df['Trade Date'] = pd.to_datetime(df['Trade Date'])
    df = df.set_index('Trade Date')
    df = df.pivot(columns='Futures', values='Close')
    df.index.name = 'date'
    df = df.sort_index(ascending=True)

    expiration_date = pd.to_datetime(os.path.basename(filepath).replace('.csv', '').replace('VX_', ''))
    return df, expiration_date


def load_and_concat_pivoted_csvs(files):
    dfs = []
    expirations = {}
    for file in files:
        try:
            df, expiration = load_and_pivot_vixterm_csv(file)
            df = df.replace(0, pd.NA)
            df.ffill(inplace=True)  # forward fill to handle missing values
            expirations[df.columns[0]] = expiration
            dfs.append(df)
        except Exception as e:
            print(f"Error loading {file}: {e}")
    if not dfs:
        return pd.DataFrame()
    df = pd.concat(dfs, axis=1, join='outer')

    return df, expirations
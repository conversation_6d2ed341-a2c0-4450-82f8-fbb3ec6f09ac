
# get all file sin to ./download folder
import os

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from itertools import combinations

BASE_PATH = os.path.dirname(os.path.abspath(__file__))
DOWNLOAD_FOLDER = os.path.join(BASE_PATH, 'download')

# LOAD DATA
df = pd.read_csv(DOWNLOAD_FOLDER + '/vixterm.csv', index_col=0)
df.index = pd.to_datetime(df.index)
expirations_df = pd.read_csv(DOWNLOAD_FOLDER + '/expirations.csv', index_col=0)
expirations_df['expiration_date'] = pd.to_datetime(expirations_df['expiration_date'])

def shift_left(row):
    values = row.dropna().tolist()  # valori validi
    values += [np.nan] * (len(row) - len(values))  # completa con NaN a destra
    return pd.Series(values, index=row.index)  # usa gli stessi nomi colonna!

df = df.apply(shift_left, axis=1)

# take the first two columns
df = df.iloc[:, [0,1]]

spread = df.iloc[:, 1] - df.iloc[:, 0]

spreads = []

for i, exp in enumerate(expirations_df['expiration_date']):
    start = (expirations_df['expiration_date'][i-1] + pd.Timedelta(days=1)) if i > 0 else None
    end = exp.strftime('%Y-%m-%d')

    # if not spread[start: end].empty and spread[start: end][0] < -1:
    spreads.append(spread[start: end].reset_index(drop=True))



spreads_df = pd.DataFrame(spreads).T
print(spreads_df)
mean = spreads_df.mean(axis=1)

# plot all spreads and mean
plt.figure(figsize=(12, 6))
#for i, col in enumerate(spreads_df.columns):
#    plt.plot(spreads_df.index, spreads_df[col], label=f'Spread {i+1}', alpha=0.5)
plt.plot(spreads_df.index, mean, label='Mean Spread', color='black', linewidth=2)
plt.title('VIX Term Structure Spreads')
plt.xlabel('Days')
plt.ylabel('Spread Value')
plt.legend()
plt.grid()
plt.tight_layout()
plt.show()
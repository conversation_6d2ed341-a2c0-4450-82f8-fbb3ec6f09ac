
# Sembra che lo spread verso la fine della prima scadenza (1M) si allarghi ripetto alla sedonda (2M)..
# Quindi vendiamo la prima e compriamo la seconda
import os
import pandas as pd
import matplotlib.pyplot as plt
from itertools import combinations

BASE_PATH = os.path.dirname(os.path.abspath(__file__))
DOWNLOAD_FOLDER = os.path.join(BASE_PATH, 'download')

# LOAD DATA
df = pd.read_csv(DOWNLOAD_FOLDER + '/vixterm.csv', index_col=0)
df.index = pd.to_datetime(df.index)
expirations_df = pd.read_csv(DOWNLOAD_FOLDER + '/expirations.csv', index_col=0)
expirations_df['expiration_date'] = pd.to_datetime(expirations_df['expiration_date'])
expirations_by_col = expirations_df['expiration_date'].to_dict()



# PARAMS
FROM = None
TO = None
TRIGGER_DAY_OF_EXPIRATION = 30


if FROM:
    df = df[(df.index > FROM)]
if TO:
    df = df[(df.index < TO)]


report = dict()
equities = []

# find first and secondd expiration for each day if exists both
available_expirations = dict()
for date, row in df.iterrows():
    all_exp = [exp for i, exp in enumerate(row.dropna().index)]
    available_expirations[date] = all_exp[:2] if len(all_exp) > 1 else []


# find relative value for each expiration available per every day, if exists
expirations_values= dict()
for date, expirations in available_expirations.items():
    expirations_values[date] = [df.loc[date, exp] for exp in expirations]

trades = []
for date, row in df.iterrows():
    in_trade = any('closed' not in trade for trade in trades)
    if not available_expirations[date]:
        continue
    exp1, exp2  = available_expirations[date]
    value1, value2 = expirations_values[date]
    days_to_first_expirations = (expirations_by_col[exp1]-date).days

    condition1 = days_to_first_expirations <= TRIGGER_DAY_OF_EXPIRATION
    condition2 = True

    if not in_trade and condition1 and condition2:
        trades.append({
            'entry_date': date,
            'sell': {
                'expiration': exp1,
                'entry_price': value1
            },
            'buy' : {
                'expiration': exp2,
                'entry_price': value2
            },
        })

    if in_trade and days_to_first_expirations <= 0:
        open_trade = next((trade for trade in trades if 'closed' not in trade), None)

        open_trade['buy']['exit_price'] = value2
        open_trade['sell']['exit_price'] = value1
        open_trade['exit_date'] = date
        open_trade['closed'] = True

prepared_trades= []
for trade in trades:
    prepared_trades.append({
        'entry_date': trade['entry_date'],
        'exit_date': trade.get('exit_date'),
        'sell_expiration': trade['sell']['expiration'],
        'buy_expiration': trade['buy']['expiration'],
        'entry_price_sell': trade['sell']['entry_price'],
        'entry_price_buy': trade['buy']['entry_price'],
        'exit_price_sell': trade.get('sell', {}).get('exit_price'),
        'exit_price_buy': trade.get('buy', {}).get('exit_price'),
        'closed': 'closed' in trade,
    })
df_trades = pd.DataFrame(prepared_trades)
# add profit column
df_trades['profit'] = (df_trades['entry_price_sell'] - df_trades['exit_price_sell']) + (df_trades['exit_price_buy'] - df_trades['entry_price_buy'])
df_trades['profit'] = (df_trades['profit']*1000).round(2)
df_trades['equity'] = df_trades['profit'].cumsum()
df_trades['dd'] = df_trades['equity'] - df_trades['equity'].cummax()


#remove best and wrost trades
df_trades = df_trades.sort_values(by='profit', ascending=False)
# order by exit date
df_trades = df_trades.sort_values(by='exit_date')
print(df_trades)
#plot equity curve with drawdown in subplots
plt.figure(figsize=(12, 6))
plt.subplot(2, 1, 1)
plt.plot(df_trades['entry_date'], df_trades['equity'], label='Equity', color='blue')
plt.title('Equity Curve')
plt.xlabel('Date')
plt.ylabel('Equity')
plt.grid()
plt.subplot(2, 1, 2)
plt.plot(df_trades['entry_date'], df_trades['dd'], label='Drawdown', color='red')
plt.title('Drawdown')
plt.xlabel('Date')
plt.ylabel('Drawdown')
plt.grid()
plt.tight_layout()
plt.savefig('final_run.png')
plt.show()

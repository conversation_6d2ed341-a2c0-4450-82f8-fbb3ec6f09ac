import asyncio
import aiohttp
import os
import pandas as pd

from utils import get_all_files_in, load_and_concat_pivoted_csvs


BASE_PATH = os.path.dirname(os.path.abspath(__file__))
DOWNLOAD_FOLDER = os.path.join(BASE_PATH, 'download')

async def fetch_json(session, url):
    async with session.get(url) as response:
        response.raise_for_status()
        return await response.json()

async def download_file(session, url, dest_folder):
    local_filename = os.path.join(dest_folder, url.split("/")[-1])
    async with session.get(url) as response:
        response.raise_for_status()
        with open(local_filename, 'wb') as f:
            while True:
                chunk = await response.content.read(1024)
                if not chunk:
                    break
                f.write(chunk)
    return local_filename

async def retrieve_and_download():
    base_url = "https://cdn.cboe.com/"
    json_url = "https://www.cboe.com/us/futures/market_statistics/historical_data/product/list/VX/"

    # Create the download folder if it does not exist
    if not os.path.exists(DOWNLOAD_FOLDER):
        os.makedirs(DOWNLOAD_FOLDER)

    # clean up the folder
    for file in os.listdir(DOWNLOAD_FOLDER):
        file_path = os.path.join(DOWNLOAD_FOLDER, file)
        try:
            if os.path.isfile(file_path):
                os.unlink(file_path)
        except Exception as e:
            print(f"Error deleting file {file_path}: {e}")

    async with aiohttp.ClientSession() as session:
        # Retrieve the JSON data from the API
        data = await fetch_json(session, json_url)
        tasks = []

        # Iterate through each year and its contracts
        for year, contracts in data.items():
            for contract in contracts:
                if contract.get("duration_type") != "M":
                    continue
                path = contract.get("path")
                if path:
                    # Prepend base_url to the path
                    file_url = base_url + path
                    tasks.append(download_file(session, file_url, DOWNLOAD_FOLDER))

        # Download all files concurrently
        downloaded_files = await asyncio.gather(*tasks)
        return downloaded_files

if __name__ == "__main__":
    downloaded = asyncio.run(retrieve_and_download())
    print("Downloaded files:")
    for file_path in downloaded:
        print(file_path)

    # build merged close vixterm csv file
    files = get_all_files_in(DOWNLOAD_FOLDER)

    # Example usage:
    df, expirations_by_col = load_and_concat_pivoted_csvs(files)
    df = df.replace(0, pd.NA)
    # remove line with all NaN values
    #df = df.dropna(how='all')
    df.to_csv(DOWNLOAD_FOLDER + '/vixterm.csv')

    expirations_df = pd.DataFrame.from_dict(expirations_by_col, orient='index', columns=['expiration_date'])
    expirations_df.to_csv(DOWNLOAD_FOLDER + '/expirations.csv')



